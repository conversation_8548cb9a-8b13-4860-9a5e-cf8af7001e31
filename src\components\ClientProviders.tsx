"use client";

import { useEffect, useState } from 'react';
import SmoothScroll from './SmoothScroll';
import ScrollProgress from './ScrollProgress';

interface ClientProvidersProps {
  children: React.ReactNode;
}

export default function ClientProviders({ children }: ClientProvidersProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <SmoothScroll>
      {isMounted && <ScrollProgress />}
      {children}
    </SmoothScroll>
  );
}
