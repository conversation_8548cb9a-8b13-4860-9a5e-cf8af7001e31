"use client";

import { Canvas } from "@react-three/fiber";
import { Suspense, useState, useEffect } from "react";
import { OrbitControls, Stars } from "@react-three/drei";
import { motion } from "motion/react";
import * as THREE from "three";









// Main scene component
function Scene() {
  return (
    <>
      <ambientLight intensity={0.6} />

      {/* Larger, more visible stars */}
      <Stars
        radius={100}
        depth={50}
        count={5000}
        factor={8}
        saturation={0}
        fade
        speed={1}
      />

      {/* Original gradient background */}
      <mesh scale={[100, 100, 100]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#0a0a0c"
          side={THREE.BackSide}
          transparent
          opacity={0.8}
        />
      </mesh>
    </>
  );
}

// Loading fallback
function Loader() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"></div>
        <div className="absolute inset-0 w-12 h-12 border-4 border-pink-500/30 border-b-pink-500 rounded-full animate-spin animate-reverse"></div>
      </div>
    </div>
  );
}

interface SpaceSceneProps {
  className?: string;
}

export default function SpaceScene({ className = "" }: SpaceSceneProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [hasWebGL, setHasWebGL] = useState(true);

  useEffect(() => {
    setIsMounted(true);

    // Check for WebGL support
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        setHasWebGL(false);
      }
    } catch (e) {
      setHasWebGL(false);
    }
  }, []);

  if (!isMounted) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <Loader />
      </div>
    );
  }

  if (!hasWebGL) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className} bg-gradient-to-br from-purple-900/20 to-pink-900/20 rounded-2xl border border-white/10`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
          <p className="text-white/70 text-sm">3D Scene Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={`w-full h-full ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    >
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        <Suspense fallback={null}>
          <Scene />
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate
            autoRotateSpeed={0.5}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
          />
        </Suspense>
      </Canvas>
    </motion.div>
  );
}
