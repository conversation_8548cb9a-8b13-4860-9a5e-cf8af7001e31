{"about": {"name": "<PERSON><PERSON>", "title": "Web Stack Developer", "subTitle": "I develop web applications, user interfaces and cloud solutions", "description": "As a Web Stack Developer with experience in both frontend and backend technologies, I have honed my skills in JavaScript, React, Node.js, PHP, and Laravel. I'm continuously expanding my expertise with frameworks like Next.js and TypeScript. I pride myself on being a quick learner and attentive listener, which allows me to collaborate effectively with clients to create efficient and scalable web solutions. My focus is on developing user-friendly applications that solve real-world problems using modern web technologies.", "quote": "Building the web, one stack at a time", "exp_year": "5", "address": "Philippines, Negros Occidental", "some_total": "90", "phoneNumber": "+63 99128388819", "contactEmail": "<EMAIL>", "avatar": {"public_id": "arone-removebg", "url": "/arone-removebg.png"}, "alternateAvatars": [{"_id": "676e772d8a9e1d0a8b0647ca", "public_id": "portfolio3/1713996156944-1hro1b.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1713996156944-1hro1b.webp"}]}, "usageMetrics": {"visits": 0, "downloads": 0}, "_id": "65b3a22c01d900e96c4219ae", "username": "portfolio3", "email": "<EMAIL>", "role": "admin", "timeline": [{"company_name": "StartupX", "summary": "Contributed to the development of a new social networking platform targeting niche communities.", "sequence": 6, "jobTitle": "Web Stack Developer", "jobLocation": "Philippines, Negros Occidental", "bulletPoints": ["Developed scalable backend services using Python and Django framework.", "Implemented user authentication and authorization using OAuth 2.0 and JWT tokens.", "Designed and optimized database schemas for performance and scalability.", "Implemented real-time messaging features using WebSockets and Redis.", "Worked closely with frontend developers to design APIs and ensure smooth integration with the frontend."], "forEducation": true, "enabled": true, "_id": "65f1fe900556c3f887e9db94"}, {"company_name": "CloudTech Innovations", "summary": "Contributed to the development of a cloud-based SaaS platform for managing IT infrastructure.", "sequence": 4, "jobTitle": "Software Developer", "jobLocation": "Manila, Philippines", "bulletPoints": ["Developed microservices using Node.js and deployed them using Docker containers.", "Implemented user authentication and authorization using OAuth 2.0 and JWT tokens.", "Integrated with cloud providers such as AWS and Azure to manage infrastructure resources.", "Designed and implemented RESTful APIs for various platform features.", "Collaborated with DevOps engineers to automate deployment and testing processes."], "forEducation": false, "enabled": true, "_id": "65f1fe4e0556c3f887e9db22"}, {"company_name": "Digital Solutions Co.", "summary": "Contributed to the development of a comprehensive CRM solution for enterprise clients.", "sequence": 3, "jobTitle": "Frontend Developer", "jobLocation": "Cebu, Philippines", "bulletPoints": ["Implemented user interface components using AngularJS and TypeScript.", "Collaborated with backend developers to integrate frontend components with RESTful APIs.", "Developed custom data visualizations and dashboards using D3.js and Chart.js.", "Participated in sprint planning meetings and provided accurate estimates for feature development.", "Conducted A/B tests and analyzed user feedback to iterate on product features."], "forEducation": false, "enabled": true, "_id": "65f1fe1e0556c3f887e9dab2"}, {"company_name": "DataTech Solutions", "summary": "Played a key role in developing a data analytics platform for a diverse range of clients.", "sequence": 2, "jobTitle": " Full Stack Developer", "jobLocation": "Iloilo, Philippines", "bulletPoints": ["Developed RESTful APIs and integrated third-party services to ingest and process large volumes of data.", "Designed and implemented responsive user interfaces using React.js and Bootstrap.", "Worked closely with data scientists to integrate machine learning models into the platform.", "Optimized database queries and improved overall system performance.", "Conducted unit tests and participated in peer code reviews to ensure code quality."], "forEducation": true, "enabled": true, "_id": "65f1fdef0556c3f887e9da44"}, {"company_name": "Tech Innovations Inc.", "summary": "Contributed to the development of a cutting-edge mobile application aimed at revolutionizing the e-commerce industry.\n", "sequence": 1, "icon": {"public_id": "portfolio3/1710357917833-0mic4.png", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357917833-0mic4.png"}, "jobTitle": "Web Stack Developer", "jobLocation": "Bacolod City, Philippines", "bulletPoints": ["Implemented core features and functionalities using React Native.", "Collaborated closely with designers and product managers to iterate on user feedback.", "Conducted code reviews and provided constructive feedback to team members.", "Resolved complex technical challenges to ensure the smooth operation of the application.", "Participated in daily stand-up meetings and sprint planning sessions."], "forEducation": false, "enabled": true, "_id": "65f1fd9e0556c3f887e9d9d8"}, {"company_name": "ThePortfolyo", "summary": "", "sequence": 1, "icon": {"public_id": "1706289470834-siro83", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706289470834-siro83"}, "jobTitle": "Web Stack Developer", "jobLocation": "Negros Occidental, Philippines", "bulletPoints": ["Design and build full website", "Optimized Site", "SEO implemented"], "forEducation": true, "enabled": true, "_id": "65b3e93feb20546ae6d46369"}], "skills": [{"enabled": true, "name": "PHP", "sequence": 19, "percentage": 90, "image": {"public_id": "php-icon", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706288734207-4rlz3m", "_id": "65b3e661b48589dfcae187f9"}, "_id": "65b3e661b48589dfcae187f9"}, {"enabled": true, "name": "<PERSON><PERSON>", "sequence": 18, "percentage": 88, "image": {"public_id": "laravel-icon", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706288708710-nrq74s", "_id": "65b3e645b48589dfcae187a4"}, "_id": "65b3e645b48589dfcae187a4"}, {"enabled": true, "name": "C++", "sequence": 17, "percentage": 87, "image": {"public_id": "1706288734207-4rlz3m", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706288734207-4rlz3m", "_id": "65b3e661b48589dfcae187f8"}, "_id": "65b3e661b48589dfcae187f7"}, {"enabled": true, "name": "Python", "sequence": 16, "percentage": 86, "image": {"public_id": "1706288708710-nrq74s", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706288708710-nrq74s", "_id": "65b3e645b48589dfcae187a3"}, "_id": "65b3e645b48589dfcae187a2"}, {"enabled": true, "name": "<PERSON>er", "sequence": 16, "percentage": 85, "image": {"public_id": "1706287155946-z2oybb", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706287155946-z2oybb", "_id": "65b3e034b48589dfcae182ce"}, "_id": "65b3e034b48589dfcae182cd"}, {"enabled": true, "name": "Figma", "sequence": 15, "percentage": 95, "image": {"public_id": "1706287138776-slfc3e", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706287138776-slfc3e", "_id": "65b3e023b48589dfcae18289"}, "_id": "65b3e023b48589dfcae18288"}, {"enabled": true, "name": "TypeScript", "sequence": 6, "percentage": 90, "image": {"public_id": "1706287104739-pm8en", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706287104739-pm8en", "_id": "65b3e001b48589dfcae18246"}, "_id": "65b3e001b48589dfcae18245"}, {"enabled": true, "name": "Node.js", "sequence": 7, "percentage": 92, "image": {"public_id": "1706286990341-tznh4", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286990341-tznh4", "_id": "65b3df8fb48589dfcae18205"}, "_id": "65b3df8fb48589dfcae18204"}, {"enabled": true, "name": "MongoDB", "sequence": 13, "percentage": 70, "image": {"public_id": "1706286966065-p2yrx9", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286966065-p2yrx9", "_id": "65b3df76b48589dfcae181c6"}, "_id": "65b3df76b48589dfcae181c5"}, {"enabled": true, "name": "Three.js", "sequence": 9, "percentage": 70, "image": {"public_id": "1706286941212-chpzl", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286941212-chpzl", "_id": "65b3df5db48589dfcae18189"}, "_id": "65b3df5db48589dfcae18188"}, {"enabled": true, "name": "Redux", "sequence": 7, "percentage": 95, "image": {"public_id": "1706286913147-nwiky8", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286913147-nwiky8", "_id": "65b3df41b48589dfcae1814e"}, "_id": "65b3df41b48589dfcae1814d"}, {"enabled": true, "name": "Javascript", "sequence": 3, "percentage": 95, "image": {"public_id": "1706288679775-y4qwn3", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706288679775-y4qwn3", "_id": "65b3e628b48589dfcae18750"}, "_id": "65b3df24b48589dfcae18114"}, {"enabled": true, "name": "Git", "sequence": 12, "percentage": 95, "image": {"public_id": "1706286842731-r0eyn", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286842731-r0eyn", "_id": "65b3defbb48589dfcae180de"}, "_id": "65b3defbb48589dfcae180dd"}, {"enabled": true, "name": "<PERSON><PERSON><PERSON>", "sequence": 11, "percentage": 95, "image": {"public_id": "1706286821409-21cuqs", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286821409-21cuqs", "_id": "65b3dee5b48589dfcae180a9"}, "_id": "65b3dee5b48589dfcae180a8"}, {"enabled": true, "name": "Sass", "sequence": 10, "percentage": 95, "image": {"public_id": "1706286780392-39i48h", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286780392-39i48h", "_id": "65b3debcb48589dfcae18076"}, "_id": "65b3debcb48589dfcae18075"}, {"enabled": true, "name": "GraphQl", "sequence": 9, "percentage": 80, "image": {"public_id": "1706286760237-gh4idq", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286760237-gh4idq", "_id": "65b3dea8b48589dfcae18045"}, "_id": "65b3dea8b48589dfcae18044"}, {"enabled": true, "name": "Vercel", "sequence": 7, "percentage": 90, "image": {"public_id": "1706286729467-rbblgb", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286729467-rbblgb", "_id": "65b3de89b48589dfcae18016"}, "_id": "65b3de89b48589dfcae18015"}, {"enabled": true, "name": "Next.js", "sequence": 6, "percentage": 90, "image": {"public_id": "1706286682511-rgp96r", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286682511-rgp96r", "_id": "65b3de5bb48589dfcae17fe9"}, "_id": "65b3de5bb48589dfcae17fe8"}, {"enabled": true, "name": "React", "sequence": 5, "percentage": 90, "image": {"public_id": "1706291833080-0loo09", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706291833080-0loo09", "_id": "65b3f27aeb20546ae6d49e76"}, "_id": "65b3de41b48589dfcae17fbd"}, {"enabled": true, "name": "Tailwind", "sequence": 4, "percentage": 99, "image": {"public_id": "1706286616701-2jka3q", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286616701-2jka3q", "_id": "65b3de19b48589dfcae17f95"}, "_id": "65b3de19b48589dfcae17f94"}, {"enabled": true, "name": "CSS", "sequence": 2, "percentage": 97, "image": {"public_id": "1706286273084-romle", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286273084-romle"}, "_id": "65b3dcc1b48589dfcae17e4d"}, {"name": "HTML", "sequence": 1, "percentage": 98, "image": {"public_id": "1706286058630-v3ax0a", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706286058630-v3ax0a"}, "_id": "65b3dbeba5c8d2db7b6164bc", "enabled": true}], "youtube": [{"url": "", "title": "Sample Video", "sequence": 1, "image": null, "embedId": "eitWM4gNZoM", "enabled": true, "_id": "65f482e5d689e5777d9457ae"}], "projects": [{"liveurl": "#", "githuburl": "#", "title": "Barangay Management System", "sequence": 17, "image": {"public_id": "barangay-management", "url": "/projects/Barangay.png"}, "images": [{"public_id": "barangay-management-1", "url": "/projects/Barangay.png"}, {"public_id": "barangay-management-2", "url": "/projects/login.png"}], "description": "Developed a comprehensive management system for local government units (Barangays) in the Philippines. Features include resident information management, certificate issuance, complaint tracking, inventory management, and financial reporting. The system streamlines administrative processes and improves service delivery to community residents.", "techStack": ["PHP", "UI", "MySQL", "Bootstrap", "j<PERSON><PERSON><PERSON>", "Ajax", "PDO"], "_id": "65b3d969d017f6b49c778c4a", "enabled": true}, {"liveurl": "#", "githuburl": "#", "title": "Rental Management System", "sequence": 16, "image": {"public_id": "rental-management", "url": "/projects/rentease.png"}, "images": [{"public_id": "rental-management", "url": "/projects/rentease.png"}, {"public_id": "rental-management-2", "url": "/projects/rentease2.png"}], "description": "Developed a comprehensive property rental management system using Laravel and PHP that simplifies property listings, tenant applications, lease management, and payment processing. Implemented secure user roles for landlords, tenants, and administrators, real-time notifications, document management, and a responsive dashboard for tracking rentals, maintenance requests, and financial reports.", "techStack": ["PHP", "<PERSON><PERSON>", "MySQL", "JavaScript", "Bootstrap", "j<PERSON><PERSON><PERSON>"], "_id": "65b3d9c8d017f6b49c778ca8", "enabled": true}, {"liveurl": "#", "githuburl": "#", "title": "E-commerce Platform", "sequence": 14, "image": {"public_id": "ecommerce-platform", "url": "/projects/ecommerce.jpg"}, "description": "Created a modern e-commerce store with React and Next.js frontend, featuring product catalog, shopping cart, secure checkout, and user accounts. Backend built with Node.js and MongoDB includes inventory management, order processing, and Stripe payment integration with 99.9% uptime.", "techStack": ["React.js", "Next.js", "Node.js", "MongoDB", "Stripe API"], "_id": "65b3d9c8d017f6b49c778ca7", "enabled": true}, {"liveurl": "#", "githuburl": "#", "title": "Real-time Chat Application", "sequence": 15, "image": {"public_id": "chat-app", "url": "/projects/chat-app.jpg"}, "description": "Developed a real-time messaging platform using Socket.io and React that supports direct messages, group chats, file sharing, and message encryption. Implemented JWT authentication, message history, typing indicators, and push notifications with MongoDB for message persistence.", "techStack": ["React.js", "Socket.io", "Express.js", "MongoDB", "JWT"], "_id": "65b3d99dd017f6b49c778c86", "enabled": true}, {"liveurl": "#", "githuburl": "#", "title": "Task Management Dashboard", "sequence": 13, "image": {"public_id": "task-management", "url": "/projects/task-management.jpg"}, "description": "Built a Kanban-style task management application with React and Redux featuring drag-and-drop functionality, task categorization, due date reminders, and team collaboration tools. Backend API with Node.js and Express handles user authentication, task persistence, and real-time updates across devices.", "techStack": ["React.js", "Redux", "Node.js", "Express", "MongoDB"], "_id": "65b3d978d017f6b49c778c67", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Healthcare Portal", "sequence": 11, "image": {"public_id": "healthcare-portal", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706285405228-98liol"}, "description": "Developed a HIPAA-compliant healthcare portal that allows patients to schedule appointments, access medical records, and communicate securely with healthcare providers. Implemented two-factor authentication, end-to-end encryption for messages, and audit logging for all data access to ensure regulatory compliance.", "techStack": ["React.js", "Node.js", "Express", "PostgreSQL", "OAuth2"], "_id": "65b3d95dd017f6b49c778c2f", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Content Management System", "sequence": 10, "image": {"public_id": "cms-system", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706285393568-zl9mcc"}, "description": "Built a headless CMS with React admin interface featuring WYSIWYG editing, image optimization, version control, and scheduled publishing. The system includes granular user permissions, content workflow management, and a RESTful API that powers multiple frontend applications with 20,000+ monthly active users.", "techStack": ["React.js", "Draft.js", "Node.js", "Express", "MongoDB"], "_id": "65b3d952d017f6b49c778c16", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Real Estate Listings Platform", "sequence": 9, "image": {"public_id": "real-estate-platform", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706285378965-91vbk"}, "description": "Created a property marketplace with advanced filtering, interactive map search, and virtual property tours using 360° panoramas. The platform features mortgage calculators, neighborhood analytics (schools, crime rates, amenities), automated listing verification, and a secure messaging system for buyers and agents.", "techStack": ["React.js", "Next.js", "Google Maps API", "Node.js", "MongoDB"], "_id": "65b3d943d017f6b49c778bff", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Financial Dashboard", "sequence": 8, "image": {"public_id": "financial-dashboard", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/*************-zdr5fl"}, "description": "Developed a personal finance application with account aggregation from multiple banks, automated expense categorization using machine learning, and investment portfolio tracking. The dashboard presents financial insights through interactive D3.js visualizations and generates monthly budget recommendations based on spending patterns.", "techStack": ["React.js", "D3.js", "Node.js", "Express", "MongoDB"], "_id": "65b3d938d017f6b49c778bea", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Learning Management System", "sequence": 7, "image": {"public_id": "learning-management", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/*************-3ja6sw"}, "description": "Built an LMS platform that supports multimedia course content, interactive assessments, and certification generation. The system includes student progress tracking, discussion forums, AI-powered learning recommendations, and integration with video conferencing for live classes. Analytics dashboard provides instructors with detailed student engagement metrics.", "techStack": ["React.js", "Redux", "Node.js", "MongoDB", "AWS S3"], "_id": "65b3d92ed017f6b49c778bd7", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Booking Reservation System", "sequence": 6, "image": {"public_id": "booking-system", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706285347449-dniw0h"}, "description": "Developed a multi-vendor appointment booking platform for service businesses with real-time availability updates, automated email/SMS reminders, and calendar synchronization (Google/Outlook). Implemented a customizable booking widget that can be embedded on any website, along with Stripe payment processing for deposits and full payments.", "techStack": ["Next.js", "React Query", "Node.js", "PostgreSQL", "Stripe"], "_id": "65b3d923d017f6b49c778bc6", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Portfolio Website Generator", "sequence": 5, "image": {"public_id": "portfolio-generator", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706285323287-nt3ya3"}, "description": "Created a SaaS platform that allows users to generate professional portfolio websites without coding. The application features a drag-and-drop interface with 20+ customizable templates, automatic image optimization, SEO tools, domain connection, and analytics. The system uses a serverless architecture for automatic scaling during peak usage.", "techStack": ["React.js", "TailwindCSS", "Node.js", "Express", "MongoDB"], "_id": "65b3d8fed017f6b49c778ba9", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Weather Dashboard Application", "sequence": 4, "image": {"public_id": "weather-dashboard", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706284852474-gh2ng"}, "description": "Built a weather application using React and Next.js that provides hourly and 7-day forecasts with detailed meteorological data (precipitation probability, wind speed, UV index, etc.). Features include location-based weather detection, animated radar maps, severe weather alerts, and historical weather data comparison with offline functionality.", "techStack": ["React.js", "Next.js", "Weather API", "Geolocation API", "TailwindCSS"], "_id": "65b3d734313a9b09a24a14a3", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Job Board Platform", "sequence": 3, "image": {"public_id": "job-board", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706284839761-ckccdj"}, "description": "Developed a specialized job marketplace connecting tech professionals with remote opportunities. Features include AI-powered resume parsing, skill matching algorithms, automated application tracking, employer verification, and detailed analytics for both job seekers and companies. The platform processes over 500 job applications daily with a 40% placement rate.", "techStack": ["React.js", "Next.js", "Node.js", "Express", "MongoDB"], "_id": "65b3d728313a9b09a24a1498", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Recipe Sharing Community", "sequence": 2, "image": {"public_id": "recipe-sharing", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706284824843-56iakg"}, "description": "Built a recipe sharing platform that automatically calculates nutritional information from ingredients. Features include recipe scaling, meal planning calendar, shopping list generation, and dietary restriction filtering (gluten-free, vegetarian, etc.). Social features allow users to follow chefs, save favorites, and participate in cooking challenges with 15,000+ active community members.", "techStack": ["React.js", "Next.js", "Node.js", "Express", "MongoDB", "TailwindCSS"], "_id": "65b3d719313a9b09a24a148f", "enabled": false}, {"liveurl": "#", "githuburl": "#", "title": "Personal Finance Tracker", "sequence": 1, "image": {"public_id": "finance-tracker", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/*************-jr7rqj"}, "description": "Created a comprehensive budgeting application that syncs with bank accounts and automatically categorizes transactions using NLP algorithms. The application generates personalized financial insights, tracks spending patterns, sets savings goals with progress tracking, and provides investment recommendations based on risk profile and financial objectives.", "techStack": ["React.js", "Next.js", "Node.js", "Express", "MongoDB"], "_id": "65b3d70d313a9b09a24a1488", "enabled": false}], "social_handles": [{"platform": "Instagram", "url": "https://www.instagram.com/", "image": {"public_id": "*************-3038n7.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/*************-3038n7.webp"}, "enabled": true, "_id": "65b3ea4feb20546ae6d4641d"}, {"platform": "LinkedIn", "url": "http://www.linkedin.com/in/", "image": {"public_id": "*************-2lfj67.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/*************-2lfj67.webp"}, "enabled": true, "_id": "65b3e99beb20546ae6d463c2"}, {"enabled": true, "platform": "Twitter", "url": "https://x.com/", "image": {"public_id": "1708718328750-d4jsq.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1708718328750-d4jsq.webp"}, "_id": "65b3e47cb48589dfcae185bd"}, {"enabled": true, "platform": "GitHub", "url": "https://github.com/AroneFritz", "image": {"public_id": "github-icon", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/github-icon.png"}, "_id": "65b3e47cb48589dfcae185be"}, {"enabled": true, "platform": "Facebook", "url": "https://www.facebook.com/arone.lamanilao/", "image": {"public_id": "1708718346601-xldm.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1708718346601-xldm.webp"}, "_id": "65b3e43cb48589dfcae1856c"}], "services": [{"name": "Laravel Development", "charge": "₱****", "desc": "Building custom web applications and APIs using the Laravel PHP framework. Specializing in scalable, secure, and maintainable solutions with clean architecture and best practices.", "enabled": true, "_id": "65f1fd690556c3f887e9d94b", "image": {"public_id": "portfolio3/1710364293287-4q1ngo.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710364293287-4q1ngo.webp"}}, {"name": "Full-Stack Web Development", "charge": "₱****", "desc": "Complete web application development using modern frameworks like React, Next.js, and Node.js. Building scalable and responsive solutions for your business.", "enabled": true, "_id": "65f1fd690556c3f887e9d94a", "image": {"public_id": "portfolio3/1710364293287-4q1ngo.webp", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710364293287-4q1ngo.webp"}}, {"name": "Frontend Development", "charge": "₱****", "desc": "Creating modern, responsive user interfaces with React, Next.js and other frontend technologies. Focus on performance, accessibility and user experience.", "image": {"public_id": "portfolio3/1710357775748-9vwq4q.jpeg", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357775748-9vwq4q.jpeg"}, "enabled": true, "_id": "65f1fd100556c3f887e9d87b"}, {"name": "Backend & API Development", "charge": "₱****", "desc": "Building robust server-side applications with Node.js, Express, and database integration. RESTful and GraphQL API development for your web applications.", "image": {"public_id": "1706290914024-725ytf", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/1706290914024-725ytf", "_id": "65b3eee8eb20546ae6d467fe"}, "_id": "65b3e33db48589dfcae1851d", "enabled": true}, {"name": "Web Performance Optimization", "charge": "₱****", "desc": "Improving your website's speed, responsiveness and overall performance. Optimizing code, assets, and implementing best practices for faster load times.", "image": {"public_id": "portfolio3/1710357668346-ke8kgt.jpeg", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357668346-ke8kgt.jpeg"}, "_id": "65b3e322b48589dfcae184d0", "enabled": true}, {"name": "Web Application Maintenance", "charge": "₱****", "desc": "Ongoing support, updates, and maintenance for your web applications to ensure they remain secure, efficient, and up-to-date with the latest technologies.", "image": {"public_id": "portfolio3/1710357680224-phijt.png", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357680224-phijt.png"}, "_id": "65b3e2f6b48589dfcae1843b", "enabled": true}, {"name": "Responsive Web Design", "charge": "₱****", "desc": "Creating websites that work seamlessly across all devices and screen sizes. Mobile-first approach with modern CSS frameworks like Tailwind.", "image": {"public_id": "portfolio3/1710357752227-6f5trs.jpeg", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357752227-6f5trs.jpeg"}, "_id": "65b3e2a6b48589dfcae183f2", "enabled": true}, {"name": "E-commerce Solutions", "charge": "₱****", "desc": "Building custom e-commerce websites with secure payment processing, inventory management, and user-friendly shopping experiences.", "image": {"public_id": "portfolio3/1710357680224-phijt.png", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357680224-phijt.png"}, "_id": "65b3e2f6b48589dfcae1843c", "enabled": true}, {"name": "Progressive Web Apps", "charge": "₱****", "desc": "Developing progressive web applications that offer native-like experiences with offline capabilities, push notifications, and fast performance.", "image": {"public_id": "portfolio3/1710357752227-6f5trs.jpeg", "url": "https://portfolio-image-store.s3.ap-south-1.amazonaws.com/portfolio3/1710357752227-6f5trs.jpeg"}, "_id": "65b3e2a6b48589dfcae183f3", "enabled": true}], "testimonials": [{"image": {"public_id": "default/profile-icon", "url": "/profile-icon.png"}, "name": "Tetete", "review": "<PERSON><PERSON><PERSON>", "position": "tettet", "enabled": true, "_id": "tetete-testimonial-id"}, {"image": {"public_id": "default/profile-icon", "url": "/profile-icon.png"}, "name": "gggg", "review": "gagaweaweaweaweaweaweaweaweaweawe", "position": "g", "enabled": true, "_id": "gggg-testimonial-id"}], "createdAt": "2024-01-26T12:14:36.514Z", "updatedAt": "2024-05-08T17:53:29.758Z", "__v": 74, "refreshToken": ""}