/* Add smooth scrolling to the entire page */
html {
  scroll-behavior: smooth;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240deg 5.79% 76.27%;
    --foreground: 240deg 4.76% 8.24%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --primary: 82.77deg 97.16% 72.35%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 225deg 6.67% 11.76%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --redis-gray: 183, 171, 152;
  }

  .dark {
    --background: 240, 10%, 5%;
    --foreground: 240deg 5.79% 76.27%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --primary: 190.29deg 51.22% 59.8%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 225deg 6.67% 11.76%;
    --secondary-foreground: 0 0% 98%;

    --border: 37, 18%, 66%, 0.5;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --opacity-color: 203, 203, 219;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground overflow-x-hidden;
    background: linear-gradient(to bottom right, rgba(49, 46, 129, 0.2), rgba(76, 29, 149, 0.15), rgba(157, 23, 77, 0.2));
    background-attachment: fixed;
    background-color: #0a0a0c;
  }

  *::-webkit-scrollbar-thumb {
    @apply bg-secondary w-0.5 rounded-full;
  }

  *::-webkit-scrollbar {
    @apply w-0;
  }
}

.blob {
  background: conic-gradient(from 2.35rad, rgba(236, 72, 153, 0.15), rgba(79, 70, 229, 0.25));
  animation: blob-move 20s infinite alternate ease-in-out;
}

.blob-secondary {
  background: conic-gradient(from 0.5rad, rgba(79, 70, 229, 0.2), rgba(236, 72, 153, 0.15));
  animation: blob-move-secondary 25s infinite alternate ease-in-out;
}

@keyframes blob-move {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(5%, 5%) rotate(5deg);
  }
  50% {
    transform: translate(-5%, 10%) rotate(-5deg);
  }
  75% {
    transform: translate(-10%, -5%) rotate(-10deg);
  }
  100% {
    transform: translate(10%, -10%) rotate(10deg);
  }
}

@keyframes blob-move-secondary {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  20% {
    transform: translate(-8%, -3%) rotate(-8deg);
  }
  40% {
    transform: translate(5%, -10%) rotate(5deg);
  }
  60% {
    transform: translate(10%, 5%) rotate(10deg);
  }
  80% {
    transform: translate(-5%, 8%) rotate(-3deg);
  }
  100% {
    transform: translate(-10%, -5%) rotate(-10deg);
  }
}

.text-reveal {
  display: inline;
  background: linear-gradient(
    to right,
    rgba(var(--opacity-color), 1) 0%,
    rgba(var(--opacity-color), 1) 50%,
    rgba(var(--opacity-color), 0.2) 50%,
    rgba(var(--opacity-color), 0.2) 100%
  );
  background-clip: text;
  background-size: 200% 100%;
  background-position: right bottom;
  color: transparent;
  background-repeat: no-repeat;
}

/* Scrollbar Hide Utility */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Animation for continuous scrolling filters */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-100% - 1rem));
  }
}

.animate-scroll {
  animation: scroll 50s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

@media (prefers-reduced-motion: reduce) {
  .animate-scroll {
    animation-play-state: paused;
  }
  
  html {
    scroll-behavior: auto;
  }
}

@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 40s;
  }
}

/* 3D Effects and Perspective */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

/* Enhanced gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Floating animation for 3D elements */
@keyframes float-3d {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: translateY(-20px) rotateX(5deg) rotateY(5deg);
  }
}

.animate-float-3d {
  animation: float-3d 6s ease-in-out infinite;
}

/* Glow effects */
.glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.glow-pink {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
