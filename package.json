{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev", "dev:alt": "cross-env NODE_OPTIONS='--inspect' next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.1", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.82", "@studio-freight/lenis": "^1.0.42", "@types/three": "^0.177.0", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "cookie": "^1.0.2", "gsap": "^3.13.0", "lenis": "^1.3.4", "lucide-react": "^0.469.0", "mongodb": "^6.15.0", "mongoose": "^8.13.2", "motion": "^11.15.0", "next": "^15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "three": "^0.177.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-config-next": "^15.1.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}