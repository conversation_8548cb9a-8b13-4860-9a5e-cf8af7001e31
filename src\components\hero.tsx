"use client";

import Link from "next/link";
import { motion } from "motion/react";
import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";

import { About } from "../utils/interface";
import { SlideIn, Transition } from "./ui/Transitions";
import { TextReveal } from "./ui/Typography";
import { ArrowUpRight } from "./ui/Icons";
import LoaderWrapper from "./LoaderWrapper";
import SpaceScene from "./3d/SpaceScene";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, useGSAP);

interface HeroProps {
  about: About;
}

const Hero = ({ about }: HeroProps) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLHeadingElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const avatarRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      // Calculate normalized mouse position (0-1)
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;

      setMousePosition({ x, y });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  // GSAP ScrollTrigger animations
  useGSAP(() => {
    const tl = gsap.timeline();

    // Hero section parallax effect
    gsap.to(heroRef.current, {
      yPercent: -50,
      ease: "none",
      scrollTrigger: {
        trigger: heroRef.current,
        start: "top top",
        end: "bottom top",
        scrub: true
      }
    });

    // Staggered text animations
    if (titleRef.current && subtitleRef.current && descriptionRef.current) {
      tl.from(titleRef.current.children, {
        y: 100,
        opacity: 0,
        duration: 1,
        stagger: 0.1,
        ease: "power3.out"
      })
      .from(subtitleRef.current.children, {
        y: 80,
        opacity: 0,
        duration: 0.8,
        stagger: 0.05,
        ease: "power3.out"
      }, "-=0.5")
      .from(descriptionRef.current, {
        y: 60,
        opacity: 0,
        duration: 0.8,
        ease: "power3.out"
      }, "-=0.3");
    }

    // Avatar floating animation
    if (avatarRef.current) {
      gsap.to(avatarRef.current, {
        y: -20,
        duration: 3,
        ease: "power2.inOut",
        yoyo: true,
        repeat: -1
      });
    }

    // CTA button entrance
    if (ctaRef.current) {
      gsap.from(ctaRef.current, {
        scale: 0.8,
        opacity: 0,
        duration: 0.6,
        delay: 1.5,
        ease: "back.out(1.7)"
      });
    }
  }, []);

  // Calculate transforms based on mouse position (px values)
  const blob1X = (mousePosition.x - 0.5) * -40; // Move opposite to cursor
  const blob1Y = (mousePosition.y - 0.5) * -40;

  const blob2X = (mousePosition.x - 0.5) * 60; // Move with cursor but more dramatically
  const blob2Y = (mousePosition.y - 0.5) * 60;

  return (
    <section ref={heroRef} className="h-dvh w-dvw overflow-hidden relative">
      {/* Realistic gradient background with depth */}
      <motion.div
        className="absolute inset-0 z-0"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 2 }}
        style={{
          background: `
            radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%,
              rgba(99, 102, 241, 0.15) 0%,
              rgba(139, 92, 246, 0.1) 25%,
              rgba(168, 85, 247, 0.08) 50%,
              rgba(236, 72, 153, 0.05) 75%,
              transparent 100%
            ),
            linear-gradient(135deg,
              rgba(15, 23, 42, 0.95) 0%,
              rgba(30, 41, 59, 0.9) 25%,
              rgba(51, 65, 85, 0.85) 50%,
              rgba(71, 85, 105, 0.8) 75%,
              rgba(100, 116, 139, 0.75) 100%
            )
          `
        }}
      />

      {/* Animated particle system */}
      <div className="absolute inset-0 z-5">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
              scale: [0, 1, 0]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Enhanced floating orbs with realistic lighting */}
      <Transition>
        <motion.div
          className="absolute top-20 left-10 w-96 h-96 z-10"
          style={{
            transform: `translate(${blob1X}px, ${blob1Y}px)`,
          }}
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          <div
            className="w-full h-full rounded-full blur-[80px] opacity-30"
            style={{
              background: `
                radial-gradient(circle at 30% 30%,
                  rgba(99, 102, 241, 0.4) 0%,
                  rgba(139, 92, 246, 0.3) 30%,
                  rgba(168, 85, 247, 0.2) 60%,
                  transparent 100%
                )
              `
            }}
          />
        </motion.div>

        <motion.div
          className="absolute bottom-20 right-10 w-80 h-80 z-10"
          style={{
            transform: `translate(${blob2X}px, ${blob2Y}px)`,
          }}
          animate={{
            scale: [1, 0.8, 1.1, 1],
            rotate: [360, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          <div
            className="w-full h-full rounded-full blur-[100px] opacity-25"
            style={{
              background: `
                radial-gradient(circle at 70% 70%,
                  rgba(236, 72, 153, 0.4) 0%,
                  rgba(219, 39, 119, 0.3) 30%,
                  rgba(190, 24, 93, 0.2) 60%,
                  transparent 100%
                )
              `
            }}
          />
        </motion.div>

        {/* Additional ambient orbs */}
        <motion.div
          className="absolute top-1/2 left-1/4 w-60 h-60 z-10"
          animate={{
            scale: [0.8, 1.3, 0.8],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div
            className="w-full h-full rounded-full blur-[90px] opacity-20"
            style={{
              background: `
                radial-gradient(circle at 50% 50%,
                  rgba(59, 130, 246, 0.3) 0%,
                  rgba(37, 99, 235, 0.2) 50%,
                  transparent 100%
                )
              `
            }}
          />
        </motion.div>
      </Transition>
      {/* Full-width 3D Space Scene Background */}
      <div className="absolute inset-0 w-full h-full z-5">
        <motion.div
          className="w-full h-full relative"
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: 2,
            delay: 0.3,
            ease: "easeOut"
          }}
        >
          <SpaceScene className="w-full h-full" />
        </motion.div>
      </div>

      <LoaderWrapper>
        <div className="relative h-full w-full flex items-center justify-center z-20">
          {/* Centered content container with enhanced backdrop */}
          <motion.div
            className="flex items-center justify-center flex-col text-center max-w-4xl mx-auto px-6 relative"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
          >


            {/* Avatar with enhanced animations */}
            <motion.div
              ref={avatarRef}
              initial={{ scale: 0.5, opacity: 0, rotateY: 180 }}
              animate={{ scale: 1, opacity: 1, rotateY: 0 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
                delay: 1.2
              }}
              className="mb-8 relative z-10"
            >
              {/* Avatar glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <motion.img
                src={about.avatar.url}
                alt={about.name}
                className="relative rounded-full size-32 md:size-40 object-cover shadow-2xl mx-auto"
                style={{
                  boxShadow: `
                    0 20px 40px -12px rgba(139, 92, 246, 0.3),
                    0 0 60px rgba(236, 72, 153, 0.2)
                  `
                }}
                whileHover={{
                  scale: 1.1,
                  boxShadow: `
                    0 25px 50px -12px rgba(139, 92, 246, 0.5),
                    0 0 80px rgba(236, 72, 153, 0.4)
                  `
                }}
                transition={{ duration: 0.4, ease: "easeOut" }}
              />
            </motion.div>

            {/* Enhanced typography with staggered animations */}
            <div className="flex flex-col items-center mb-8 relative z-10">
              <motion.h2
                ref={titleRef}
                className="text-4xl md:text-6xl lg:text-7xl font-bold overflow-hidden text-center mb-4"
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.5 }}
              >
                <motion.span
                  className="inline-block"
                  initial={{ opacity: 0, rotateX: 90 }}
                  animate={{ opacity: 1, rotateX: 0 }}
                  transition={{ duration: 0.6, delay: 1.7 }}
                >
                  Hello!
                </motion.span>{" "}
                <motion.span
                  className="inline-block"
                  initial={{ opacity: 0, rotateX: 90 }}
                  animate={{ opacity: 1, rotateX: 0 }}
                  transition={{ duration: 0.6, delay: 1.9 }}
                >
                  I&apos;m
                </motion.span>{" "}
                <motion.span
                  className="inline-block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent"
                  initial={{ opacity: 0, rotateX: 90, scale: 0.8 }}
                  animate={{ opacity: 1, rotateX: 0, scale: 1 }}
                  transition={{ duration: 0.8, delay: 2.1, type: "spring", stiffness: 200 }}
                >
                  {about.name}
                </motion.span>
              </motion.h2>

              <motion.h1
                ref={subtitleRef}
                className="text-3xl md:text-5xl lg:text-6xl overflow-hidden text-center"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 2.3 }}
              >
                <motion.span
                  className="inline-block bg-gradient-to-r from-white via-white/90 to-white/70 bg-clip-text text-transparent"
                  animate={{
                    backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  style={{
                    backgroundSize: "200% 200%"
                  }}
                >
                  {about.title.split(' ')[0]}
                </motion.span>{" "}
                <motion.span
                  className="inline-block bg-gradient-to-r from-white/70 via-white/90 to-white bg-clip-text text-transparent"
                  animate={{
                    backgroundPosition: ["100% 50%", "0% 50%", "100% 50%"]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    ease: "linear",
                    delay: 2.5
                  }}
                  style={{
                    backgroundSize: "200% 200%"
                  }}
                >
                  {about.title.split(' ').slice(1).join(' ')}
                </motion.span>
              </motion.h1>
            </div>

            {/* Enhanced description */}
            <motion.div
              className="w-full max-w-2xl relative z-10"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 2.5 }}
            >
              <motion.p
                ref={descriptionRef}
                className="text-lg md:text-xl py-4 text-center leading-relaxed text-white/80"
                animate={{
                  opacity: [0.8, 1, 0.8]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                {about.subTitle}
              </motion.p>
            </motion.div>

            {/* Enhanced CTA button */}
            <motion.div
              ref={ctaRef}
              className="mt-8 relative z-10"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 2.8, type: "spring", stiffness: 200 }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <Link
                href={"#contact"}
                className="relative px-8 py-4 rounded-full flex items-center gap-3 group bg-white/10 hover:bg-white/20 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/30"
              >
                <motion.span
                  className="text-white font-medium"
                  whileHover={{ scale: 1.05 }}
                >
                  <TextReveal>Let&apos;s talk</TextReveal>
                </motion.span>
                <motion.div
                  whileHover={{ rotate: 45, scale: 1.2 }}
                  transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
                  className="text-purple-400"
                >
                  <ArrowUpRight />
                </motion.div>
              </Link>
            </motion.div>
          </motion.div>

          {/* Enhanced down arrow indicator */}
          <motion.div
            className="absolute bottom-10 left-1/2 -translate-x-1/2 cursor-pointer z-30"
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: 1,
              y: [0, 10, 0],
            }}
            transition={{
              opacity: { duration: 0.6, delay: 3.2 },
              y: {
                duration: 1.5,
                repeat: Infinity,
                repeatType: "loop",
                ease: "easeInOut"
              }
            }}
            onClick={() => {
              const aboutSection = document.querySelector('#about');
              if (aboutSection) {
                aboutSection.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start'
                });
              }
            }}
            whileHover={{ scale: 1.3 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              className="p-3 rounded-full bg-white/10"
              whileHover={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)'
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-white/70 hover:text-white transition-colors duration-300"
              >
                <path d="M12 5v14M5 12l7 7 7-7"/>
              </svg>
            </motion.div>
          </motion.div>
        </div>
      </LoaderWrapper>
    </section>
  );
};

export default Hero;
