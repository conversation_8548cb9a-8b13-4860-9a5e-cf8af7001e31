"use client";

import { useEffect, useState } from 'react';
import { motion } from 'motion/react';

export default function ScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    const updateScrollProgress = () => {
      // Get scroll position
      const scrollTop = window.scrollY || document.documentElement.scrollTop;

      // Calculate document height more reliably
      const docHeight = Math.max(
        document.body.scrollHeight,
        document.body.offsetHeight,
        document.documentElement.clientHeight,
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      );

      const windowHeight = window.innerHeight;
      const scrollableHeight = docHeight - windowHeight;

      // Prevent division by zero and ensure valid progress
      if (scrollableHeight <= 0) {
        setScrollProgress(0);
        return;
      }

      const progress = Math.min(Math.max(scrollTop / scrollableHeight, 0), 1);
      setScrollProgress(progress);
    };

    // Add a small delay to ensure DOM is fully loaded
    const timeoutId = setTimeout(() => {
      updateScrollProgress();
    }, 100);

    window.addEventListener('scroll', updateScrollProgress, { passive: true });
    window.addEventListener('resize', updateScrollProgress, { passive: true });

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('scroll', updateScrollProgress);
      window.removeEventListener('resize', updateScrollProgress);
    };
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <>
      {/* Top progress bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 origin-left z-50"
        style={{
          scaleX: scrollProgress,
          transformOrigin: "left center"
        }}
      />

      {/* Circular progress indicator or scroll to top button */}
      <motion.div
        className="fixed bottom-8 right-8 w-16 h-16 z-50"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 2, duration: 0.5 }}
      >
        {scrollProgress >= 0.99 ? (
          // Scroll to top button when at 100%
          <motion.button
            className="w-full h-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform duration-300 shadow-lg hover:shadow-purple-500/30"
            onClick={() => {
              window.scrollTo({
                top: 0,
                behavior: 'smooth'
              });
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, rotate: 180 }}
            animate={{ opacity: 1, rotate: 0 }}
            transition={{ duration: 0.5 }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-white"
            >
              <path d="M12 19V5M5 12l7-7 7 7"/>
            </svg>
          </motion.button>
        ) : (
          // Progress indicator when not at 100%
          <>
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="rgba(255, 255, 255, 0.1)"
                strokeWidth="2"
                fill="none"
              />
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="url(#gradient)"
                strokeWidth="3"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${scrollProgress * 283} 283`}
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#8b5cf6" />
                  <stop offset="50%" stopColor="#ec4899" />
                  <stop offset="100%" stopColor="#3b82f6" />
                </linearGradient>
              </defs>
            </svg>

            {/* Percentage text */}
            <div className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white/70">
              <span>
                {Math.round(scrollProgress * 100)}%
              </span>
            </div>
          </>
        )}
      </motion.div>
    </>
  );
}
